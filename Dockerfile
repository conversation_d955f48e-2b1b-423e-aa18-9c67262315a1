# Build stage
FROM golang:1.25-alpine3.22 AS builder
ARG TARGETOS
ARG TARGETARCH
ENV GOPROXY=https://goproxy.cn,direct
ENV GO111MODULE=on

# Install git and ca-certificates
RUN apk add --no-cache git ca-certificates

# Set working directory
WORKDIR /app

# Copy source code
COPY . .

# Build the application
RUN GOOS=${TARGETOS} GOARCH=${TARGETARCH} CGO_ENABLED=0 go build -a -o media-nexus ./cmd/media-nexus

# Final stage
FROM alpine:3.22
ENV GIN_MODE=release
ENV TZ=Asia/Shanghai

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/media-nexus .

# Copy config
COPY config.yaml .

# Create log directory
RUN mkdir -p /var/log && chown appuser:appgroup /var/log

# Change ownership of app directory
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose ports
EXPOSE 8080 5060/udp

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./media-nexus", "-config", "config.yaml"]
