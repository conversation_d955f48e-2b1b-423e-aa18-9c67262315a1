package webadmin

import (
	"context"
	"embed"
	"fmt"
	"html/template"
	"log/slog"
	"net/http"
	"strconv"
	"time"

	"media-nexus/internal/config"

	"github.com/gin-gonic/gin"
)

//go:embed templates/* static/*
var embedFS embed.FS

// Server Web管理界面服务器
type Server struct {
	config        *config.WebAdminConfig
	configManager *config.Manager
	server        *http.Server
	router        *gin.Engine
}

// NewServer 创建新的Web管理服务器
func NewServer(webConfig *config.WebAdminConfig, configManager *config.Manager) *Server {
	// 设置Gin为发布模式
	gin.SetMode(gin.ReleaseMode)

	s := &Server{
		config:        webConfig,
		configManager: configManager,
		router:        gin.New(),
	}

	s.setupRoutes()
	return s
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 添加中间件
	s.router.Use(gin.Logger())
	s.router.Use(gin.Recovery())
	s.router.Use(s.corsMiddleware())

	// 配置管理路由组
	configGroup := s.router.Group(s.config.Path)
	{
		// 静态文件服务
		configGroup.GET("/static/*filepath", s.serveStaticFiles)

		// 配置界面
		configGroup.GET("/", s.handleConfigPage)
		configGroup.GET("/index.html", s.handleConfigPage)

		// API接口
		api := configGroup.Group("/api")
		{
			api.GET("/config", s.handleGetConfig)
			api.POST("/config", s.handleUpdateConfig)
			api.GET("/status", s.handleGetStatus)
		}
	}
}

// corsMiddleware CORS中间件
func (s *Server) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// serveStaticFiles 服务静态文件
func (s *Server) serveStaticFiles(c *gin.Context) {
	filePath := c.Param("filepath")
	data, err := embedFS.ReadFile("static" + filePath)
	if err != nil {
		c.String(404, "File not found")
		return
	}

	// 设置正确的Content-Type
	contentType := "text/plain"
	if len(filePath) > 3 {
		switch filePath[len(filePath)-3:] {
		case ".css":
			contentType = "text/css"
		case ".js":
			contentType = "application/javascript"
		}
	}

	c.Header("Content-Type", contentType)
	c.Data(200, contentType, data)
}

// handleConfigPage 处理配置页面请求
func (s *Server) handleConfigPage(c *gin.Context) {
	tmplData, err := embedFS.ReadFile("templates/config.html")
	if err != nil {
		c.String(500, "Failed to load template: %v", err)
		return
	}

	tmpl, err := template.New("config").Parse(string(tmplData))
	if err != nil {
		c.String(500, "Failed to parse template: %v", err)
		return
	}

	currentConfig := s.configManager.GetConfig()

	data := struct {
		Config     *config.Config
		ConfigPath string
		BasePath   string
	}{
		Config:     currentConfig,
		ConfigPath: s.configManager.GetConfigPath(),
		BasePath:   s.config.Path,
	}

	c.Header("Content-Type", "text/html; charset=utf-8")
	if err := tmpl.Execute(c.Writer, data); err != nil {
		c.String(500, "Failed to execute template: %v", err)
	}
}

// handleGetConfig 处理获取配置的API请求
func (s *Server) handleGetConfig(c *gin.Context) {
	config := s.configManager.GetConfig()
	c.JSON(200, gin.H{
		"success": true,
		"data":    config,
	})
}

// handleUpdateConfig 处理更新配置的API请求
func (s *Server) handleUpdateConfig(c *gin.Context) {
	var newConfig config.Config
	if err := c.ShouldBindJSON(&newConfig); err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"error":   "Invalid JSON format: " + err.Error(),
		})
		return
	}

	// 验证配置
	if err := s.validateConfig(&newConfig); err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"error":   "Invalid configuration: " + err.Error(),
		})
		return
	}

	// 更新配置
	if err := s.configManager.UpdateConfig(&newConfig); err != nil {
		slog.Error("Failed to update config", "error", err)
		c.JSON(500, gin.H{
			"success": false,
			"error":   "Failed to update configuration: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Configuration updated successfully. Please restart the container to apply changes.",
	})
}

// handleGetStatus 处理获取状态的API请求
func (s *Server) handleGetStatus(c *gin.Context) {
	c.JSON(200, gin.H{
		"success":   true,
		"timestamp": time.Now().Unix(),
		"server":    "MediaNexus Config Manager",
		"version":   "1.0.0",
	})
}

// validateConfig 验证配置
func (s *Server) validateConfig(cfg *config.Config) error {
	// 验证端口范围
	if cfg.Server.HTTPPort <= 0 || cfg.Server.HTTPPort > 65535 {
		return fmt.Errorf("invalid HTTP port: %d", cfg.Server.HTTPPort)
	}
	if cfg.Server.SIPPort <= 0 || cfg.Server.SIPPort > 65535 {
		return fmt.Errorf("invalid SIP port: %d", cfg.Server.SIPPort)
	}
	if cfg.WebAdmin.Port <= 0 || cfg.WebAdmin.Port > 65535 {
		return fmt.Errorf("invalid WebAdmin port: %d", cfg.WebAdmin.Port)
	}

	// 验证SIP ID和域
	if cfg.Server.SIPID == "" {
		return fmt.Errorf("SIP ID cannot be empty")
	}
	if cfg.Server.SIPDomain == "" {
		return fmt.Errorf("SIP domain cannot be empty")
	}

	// 验证日志级别
	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	if !validLogLevels[cfg.Log.Level] {
		return fmt.Errorf("invalid log level: %s", cfg.Log.Level)
	}

	// 验证间隔时间
	if cfg.Server.CatalogQueryInterval <= 0 {
		return fmt.Errorf("catalog query interval must be positive")
	}
	if cfg.Server.DeviceExpireTimeout <= 0 {
		return fmt.Errorf("device expire timeout must be positive")
	}

	return nil
}

// Start 启动Web服务器
func (s *Server) Start(ctx context.Context) error {
	s.server = &http.Server{
		Addr:    ":" + strconv.Itoa(s.config.Port),
		Handler: s.router,
	}

	// 在goroutine中启动服务器
	go func() {
		slog.Info("Starting web admin server", "port", s.config.Port, "path", s.config.Path)
		if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			slog.Error("Web admin server error", "error", err)
		}
	}()

	return nil
}

// Stop 停止Web服务器
func (s *Server) Stop(ctx context.Context) error {
	if s.server != nil {
		slog.Info("Stopping web admin server...")
		return s.server.Shutdown(ctx)
	}
	return nil
}
