// 配置管理 JavaScript

class ConfigManager {
    constructor() {
        this.form = document.getElementById('configForm');
        this.saveBtn = document.getElementById('saveBtn');
        this.resetBtn = document.getElementById('resetBtn');
        this.notification = document.getElementById('notification');
        
        this.originalData = {};
        this.basePath = this.getBasePath();
        
        this.init();
    }
    
    init() {
        // 保存原始数据
        this.saveOriginalData();
        
        // 绑定事件
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        this.resetBtn.addEventListener('click', () => this.handleReset());
        
        // 定期检查配置更新
        this.startConfigPolling();
    }
    
    getBasePath() {
        // 从当前URL获取base path
        const path = window.location.pathname;
        const parts = path.split('/');
        parts.pop(); // 移除最后的文件名或空字符串
        return parts.join('/') || '/config';
    }
    
    saveOriginalData() {
        const formData = new FormData(this.form);
        this.originalData = this.formDataToObject(formData);
    }
    
    formDataToObject(formData) {
        const obj = {};
        for (const [key, value] of formData.entries()) {
            this.setNestedProperty(obj, key, value);
        }
        return obj;
    }
    
    setNestedProperty(obj, path, value) {
        const keys = path.split('.');
        let current = obj;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current)) {
                current[key] = {};
            }
            current = current[key];
        }
        
        const lastKey = keys[keys.length - 1];
        
        // 处理不同类型的值
        if (value === 'on') {
            // 复选框选中状态
            current[lastKey] = true;
        } else if (value === '' && this.form.querySelector(`[name="${path}"]`).type === 'checkbox') {
            // 复选框未选中状态
            current[lastKey] = false;
        } else if (!isNaN(value) && value !== '') {
            // 数字类型
            current[lastKey] = parseInt(value, 10);
        } else {
            // 字符串类型
            current[lastKey] = value;
        }
    }
    
    async handleSubmit(e) {
        e.preventDefault();
        
        this.setButtonLoading(this.saveBtn, true);
        
        try {
            // 获取表单数据
            const formData = new FormData(this.form);
            const configData = this.formDataToObject(formData);
            
            // 处理复选框（未选中的复选框不会出现在FormData中）
            const checkboxes = this.form.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (!formData.has(checkbox.name)) {
                    this.setNestedProperty(configData, checkbox.name, false);
                }
            });
            
            console.log('Submitting config:', configData);
            
            // 发送请求
            const response = await fetch(`${this.basePath}/api/config`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(configData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('配置保存成功！请重启容器使配置生效。', 'success');
                this.saveOriginalData(); // 更新原始数据
            } else {
                throw new Error(result.error || '保存失败');
            }
            
        } catch (error) {
            console.error('Error saving config:', error);
            this.showNotification(`保存失败：${error.message}`, 'error');
        } finally {
            this.setButtonLoading(this.saveBtn, false);
        }
    }
    
    handleReset() {
        if (confirm('确定要重置所有更改吗？')) {
            this.form.reset();
            this.populateForm(this.originalData);
            this.showNotification('表单已重置到原始状态', 'info');
        }
    }
    
    populateForm(data) {
        // 递归填充表单
        this.populateFormRecursive(data, '');
    }
    
    populateFormRecursive(obj, prefix) {
        for (const [key, value] of Object.entries(obj)) {
            const fieldName = prefix ? `${prefix}.${key}` : key;
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = Boolean(value);
                } else {
                    field.value = value;
                }
            } else if (typeof value === 'object' && value !== null) {
                this.populateFormRecursive(value, fieldName);
            }
        }
    }
    
    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.classList.add('loading');
            button.setAttribute('data-original-text', button.textContent);
            button.textContent = '保存中...';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.textContent = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    }
    
    showNotification(message, type = 'info') {
        this.notification.textContent = message;
        this.notification.className = `notification ${type}`;
        this.notification.style.display = 'block';
        
        // 自动隐藏
        setTimeout(() => {
            this.notification.style.display = 'none';
        }, 5000);
    }
    
    async startConfigPolling() {
        // 每30秒检查一次配置是否有外部更新
        setInterval(async () => {
            try {
                const response = await fetch(`${this.basePath}/api/config`);
                const result = await response.json();
                
                if (result.success) {
                    // 这里可以添加逻辑来检测配置是否被外部修改
                    // 如果需要，可以提示用户重新加载页面
                }
            } catch (error) {
                console.warn('Failed to check config updates:', error);
            }
        }, 30000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ConfigManager();
});