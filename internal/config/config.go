package config

import (
	"fmt"
	"log/slog"
	"os"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Log      LogConfig      `mapstructure:"log"`
	WebAdmin WebAdminConfig `mapstructure:"webadmin"`
}

// ServerConfig represents server configuration
type ServerConfig struct {
	HTTPPort             int    `mapstructure:"http_port"`
	SIPIP                string `mapstructure:"sip_ip"`
	SIPPort              int    `mapstructure:"sip_port"`
	SIPID                string `mapstructure:"sip_id"`
	SIPDomain            string `mapstructure:"sip_domain"`
	CatalogQueryInterval int    `mapstructure:"catalog_query_interval"` // catalog查询间隔(秒)
	DeviceExpireTimeout  int    `mapstructure:"device_expire_timeout"`  // 设备过期超时时间(秒)
}

// LogConfig represents log configuration
type LogConfig struct {
	Level string `mapstructure:"level"`
	Path  string `mapstructure:"path"`
}

// WebAdminConfig represents web admin configuration
type WebAdminConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Port    int    `mapstructure:"port"`
	Path    string `mapstructure:"path"`
}

// Load loads configuration from file
func Load(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// Set default values
	viper.SetDefault("server.http_port", 8080)
	viper.SetDefault("server.sip_ip", "0.0.0.0")
	viper.SetDefault("server.sip_port", 5060)
	viper.SetDefault("server.sip_id", "44010000002000000099")
	viper.SetDefault("server.sip_domain", "4401000000")
	viper.SetDefault("server.catalog_query_interval", 60) // 默认60秒
	viper.SetDefault("server.device_expire_timeout", 300) // 默认300秒

	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.path", "/var/log/media-nexus.log")

	viper.SetDefault("webadmin.enabled", true)
	viper.SetDefault("webadmin.port", 8081)
	viper.SetDefault("webadmin.path", "/config")

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// GetLogLevel returns slog.Level based on config
func (c *LogConfig) GetLogLevel() slog.Level {
	switch c.Level {
	case "debug":
		return slog.LevelDebug
	case "info":
		return slog.LevelInfo
	case "warn":
		return slog.LevelWarn
	case "error":
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

// IsDebugMode returns true if log level is debug
func (c *LogConfig) IsDebugMode() bool {
	return c.Level == "debug"
}

// SetupLogger configures the global logger
func (c *LogConfig) SetupLogger() error {
	var handler slog.Handler

	opts := &slog.HandlerOptions{
		Level: c.GetLogLevel(),
	}

	if c.IsDebugMode() {
		opts.AddSource = true
		handler = slog.NewTextHandler(os.Stdout, opts)
	} else {
		// Write to file if path is specified, otherwise write to stdout
		if c.Path != "" {
			file, err := os.OpenFile(c.Path, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0o666)
			if err != nil {
				return fmt.Errorf("failed to open log file: %w", err)
			}
			handler = slog.NewJSONHandler(file, opts)
		} else {
			handler = slog.NewTextHandler(os.Stdout, opts)
		}
	}

	logger := slog.New(handler)
	slog.SetDefault(logger)

	return nil
}
