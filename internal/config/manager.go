package config

import (
	"fmt"
	"log/slog"
	"sync"

	"github.com/spf13/viper"
)

// Manager 线程安全的配置管理器
type Manager struct {
	mu         sync.RWMutex
	config     *Config
	configPath string
	viper      *viper.Viper
}

// NewManager 创建新的配置管理器
func NewManager(configPath string) (*Manager, error) {
	m := &Manager{
		configPath: configPath,
		viper:      viper.New(),
	}

	if err := m.loadConfig(); err != nil {
		return nil, fmt.Errorf("failed to load initial config: %w", err)
	}

	return m, nil
}

// loadConfig 加载配置文件
func (m *Manager) loadConfig() error {
	m.viper.SetConfigFile(m.configPath)
	m.viper.SetConfigType("yaml")

	// Set default values
	m.setDefaults()

	if err := m.viper.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := m.viper.Unmarshal(&config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	m.mu.Lock()
	m.config = &config
	m.mu.Unlock()

	return nil
}

// setDefaults 设置默认值
func (m *Manager) setDefaults() {
	m.viper.SetDefault("server.http_port", 8080)
	m.viper.SetDefault("server.sip_ip", "0.0.0.0")
	m.viper.SetDefault("server.sip_port", 5060)
	m.viper.SetDefault("server.sip_id", "44010000002000000099")
	m.viper.SetDefault("server.sip_domain", "4401000000")
	m.viper.SetDefault("server.catalog_query_interval", 60)
	m.viper.SetDefault("server.device_expire_timeout", 300)

	m.viper.SetDefault("log.level", "info")
	m.viper.SetDefault("log.path", "/var/log/media-nexus.log")

	m.viper.SetDefault("webadmin.enabled", true)
	m.viper.SetDefault("webadmin.port", 8081)
	m.viper.SetDefault("webadmin.path", "/config")
}

// GetConfig 获取当前配置（线程安全）
func (m *Manager) GetConfig() *Config {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 返回配置的深拷贝
	configCopy := *m.config
	return &configCopy
}

// UpdateConfig 更新配置并保存到文件（线程安全）
func (m *Manager) UpdateConfig(newConfig *Config) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 更新viper中的值
	m.viper.Set("server.http_port", newConfig.Server.HTTPPort)
	m.viper.Set("server.sip_ip", newConfig.Server.SIPIP)
	m.viper.Set("server.sip_port", newConfig.Server.SIPPort)
	m.viper.Set("server.sip_id", newConfig.Server.SIPID)
	m.viper.Set("server.sip_domain", newConfig.Server.SIPDomain)
	m.viper.Set("server.catalog_query_interval", newConfig.Server.CatalogQueryInterval)
	m.viper.Set("server.device_expire_timeout", newConfig.Server.DeviceExpireTimeout)

	m.viper.Set("log.level", newConfig.Log.Level)
	m.viper.Set("log.path", newConfig.Log.Path)

	m.viper.Set("webadmin.enabled", newConfig.WebAdmin.Enabled)
	m.viper.Set("webadmin.port", newConfig.WebAdmin.Port)
	m.viper.Set("webadmin.path", newConfig.WebAdmin.Path)

	// 保存到文件
	if err := m.viper.WriteConfig(); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	// 更新内存中的配置
	m.config = newConfig

	slog.Info("Configuration updated and saved (restart required)", "path", m.configPath)
	return nil
}

// GetConfigPath 获取配置文件路径
func (m *Manager) GetConfigPath() string {
	return m.configPath
}
