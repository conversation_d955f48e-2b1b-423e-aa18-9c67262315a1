# GitLab CI/CD 配置文件 - 多架构Docker镜像构建
# 支持构建 amd64 和 arm64 架构的Docker镜像

stages:
  - build_docker

variables:
  PLATFORMS: "linux/amd64,linux/arm64"

build_docker:
  stage: build_docker
  image: *************/docker:24-dind
  services:
    - *************/docker:24-dind
  before_script:
    # 检查Docker buildx是否支持多架构
    - |
      if ! docker buildx inspect | grep -q "linux/amd64"; then
        echo "ERROR: amd64 platform not supported"
        exit 1
      fi
      if ! docker buildx inspect | grep -q "linux/arm64"; then
        echo "ERROR: arm64 platform not supported"
        exit 1
      fi
    # 登录到Docker仓库
    - docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD $DOCKER_IMAGE_SERVER
    - docker buildx version
    - docker context create builder-context || true
    # docs: https://docs.docker.com/build/buildkit/configure/
    - docker buildx create --help
    - docker buildx create --name multiarch-builder --driver docker-container --buildkitd-config buildkitd.toml --bootstrap --use builder-context
    - docker buildx inspect multiarch-builder
    - docker buildx use multiarch-builder
  script:
    # 构建多架构镜像
    - set -eo pipefail
    - docker buildx build --platform "${PLATFORMS}" --progress plain --tag "${DOCKER_IMAGE_SERVER}/media-nexus:${CI_COMMIT_TAG}" --push .
  after_script:
    # 登出Docker仓库
    - docker logout $DOCKER_IMAGE_SERVER
    - docker buildx rm multiarch-builder || true
  rules:
    # 仅在标签推送时运行
    - if: $CI_COMMIT_TAG
    # 手动触发
    - when: manual
      allow_failure: true
  tags:
    - shared
